using FlexirouteRAGChatbot.API.Services;
using Microsoft.AspNetCore.Mvc;

namespace FlexirouteRAGChatbot.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class DocumentsController : ControllerBase
    {
        private readonly IRagService _ragService;
        private readonly ILogger<DocumentsController> _logger;

        public DocumentsController(IRagService ragService, ILogger<DocumentsController> logger)
        {
            _ragService = ragService;
            _logger = logger;
        }

        [HttpPost("index-folder")]
        public async Task<IActionResult> IndexFolder([FromBody] IndexFolderRequest request)
        {
            try
            {
                _logger.LogInformation("Indexing folder: {FolderPath}", request.FolderPath);
                
                var processedCount = await _ragService.IndexDocumentsFromFolderAsync(request.FolderPath);
                
                return Ok(new
                {
                    Success = true,
                    ProcessedDocuments = processedCount,
                    Message = $"Successfully indexed {processedCount} documents"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error indexing folder: {FolderPath}", request.FolderPath);
                return BadRequest(new
                {
                    Success = false,
                    Message = ex.Message
                });
            }
        }

        [HttpPost("index-sharepoint")]
        public async Task<IActionResult> IndexSharePoint([FromBody] IndexSharePointRequest request)
        {
            try
            {
                _logger.LogInformation("Indexing SharePoint: {SiteUrl}/{LibraryName}", request.SiteUrl, request.LibraryName);
                
                var processedCount = await _ragService.IndexDocumentsFromSharePointAsync(request.SiteUrl, request.LibraryName);
                
                return Ok(new
                {
                    Success = true,
                    ProcessedDocuments = processedCount,
                    Message = $"Successfully indexed {processedCount} documents from SharePoint"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error indexing SharePoint: {SiteUrl}/{LibraryName}", request.SiteUrl, request.LibraryName);
                return BadRequest(new
                {
                    Success = false,
                    Message = ex.Message
                });
            }
        }

        [HttpGet("status")]
        public async Task<IActionResult> GetIndexStatus()
        {
            try
            {
                var status = await _ragService.GetIndexStatusAsync();
                return Ok(status);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting index status");
                return BadRequest(new
                {
                    Success = false,
                    Message = ex.Message
                });
            }
        }
    }

    public class IndexFolderRequest
    {
        public string FolderPath { get; set; } = string.Empty;
    }

    public class IndexSharePointRequest
    {
        public string SiteUrl { get; set; } = string.Empty;
        public string LibraryName { get; set; } = string.Empty;
    }
}
