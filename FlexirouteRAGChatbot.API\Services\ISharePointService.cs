namespace FlexirouteRAGChatbot.API.Services
{
    public class SharePointDocument
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Content { get; set; } = string.Empty;
        public string Url { get; set; } = string.Empty;
        public DateTime LastModified { get; set; }
        public string ContentType { get; set; } = string.Empty;
        public long Size { get; set; }
    }

    public interface ISharePointService
    {
        /// <summary>
        /// Retrieves documents from SharePoint
        /// </summary>
        /// <param name="siteUrl">SharePoint site URL</param>
        /// <param name="libraryName">Document library name</param>
        /// <returns>Collection of SharePoint documents</returns>
        Task<IEnumerable<SharePointDocument>> GetDocumentsAsync(string siteUrl, string libraryName);

        /// <summary>
        /// Downloads content of a specific document
        /// </summary>
        /// <param name="documentUrl">Document URL</param>
        /// <returns>Document content as string</returns>
        Task<string> GetDocumentContentAsync(string documentUrl);

        /// <summary>
        /// Checks if SharePoint service is configured and available
        /// </summary>
        /// <returns>True if service is available, false otherwise</returns>
        Task<bool> IsAvailableAsync();
    }
}
