using iTextSharp.text.pdf;
using iTextSharp.text.pdf.parser;
using System.Text;

namespace FlexirouteRAGChatbot.API.Services
{
    public class RagService : IRagService
    {
        private readonly IEmbeddingService _embeddingService;
        private readonly IVectorStoreService _vectorStoreService;
        private readonly ILLMService _llmService;
        private readonly ISharePointService _sharePointService;
        private readonly ILogger<RagService> _logger;

        public RagService(
            IEmbeddingService embeddingService,
            IVectorStoreService vectorStoreService,
            ILLMService llmService,
            ISharePointService sharePointService,
            ILogger<RagService> logger)
        {
            _embeddingService = embeddingService;
            _vectorStoreService = vectorStoreService;
            _llmService = llmService;
            _sharePointService = sharePointService;
            _logger = logger;
        }

        public async Task<ChatResponse> ProcessChatRequestAsync(ChatRequest request)
        {
            try
            {
                _logger.LogInformation("Processing chat request: {Question}", request.Question);

                // Generate embedding for the user's question
                var queryEmbedding = await _embeddingService.GenerateEmbeddingAsync(request.Question);

                // Search for relevant document chunks
                var searchResults = await _vectorStoreService.SearchAsync(queryEmbedding, topK: 3);
                var relevantChunks = searchResults.ToList();

                if (!relevantChunks.Any())
                {
                    return new ChatResponse
                    {
                        Answer = "I don't have enough information to answer your question. Please make sure documents are indexed.",
                        ConversationId = request.ConversationId ?? Guid.NewGuid().ToString(),
                        IsSuccess = true,
                        RetrievedChunks = 0
                    };
                }

                // Build context from retrieved chunks
                var context = string.Join("\n\n", relevantChunks.Select(r => r.Chunk.Content));
                var sourceDocuments = relevantChunks.Select(r => r.Chunk.SourceDocument).Distinct().ToList();

                // Build prompt for LLM
                var prompt = BuildPrompt(request.Question, context);

                // Generate response using LLM
                var llmRequest = new LLMRequest
                {
                    Prompt = prompt,
                    SystemMessage = "You are a helpful assistant that answers questions based on the provided context. If the context doesn't contain relevant information, say so clearly.",
                    Temperature = 0.7f,
                    MaxTokens = 1000
                };

                var llmResponse = await _llmService.GenerateResponseAsync(llmRequest);

                return new ChatResponse
                {
                    Answer = llmResponse.Content,
                    ConversationId = request.ConversationId ?? Guid.NewGuid().ToString(),
                    SourceDocuments = sourceDocuments,
                    IsSuccess = llmResponse.IsSuccess,
                    ErrorMessage = llmResponse.ErrorMessage,
                    RetrievedChunks = relevantChunks.Count
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing chat request");
                return new ChatResponse
                {
                    Answer = "I'm sorry, but I encountered an error while processing your request.",
                    ConversationId = request.ConversationId ?? Guid.NewGuid().ToString(),
                    IsSuccess = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        public async Task<int> IndexDocumentsFromFolderAsync(string folderPath)
        {
            try
            {
                _logger.LogInformation("Indexing documents from folder: {FolderPath}", folderPath);

                if (!Directory.Exists(folderPath))
                {
                    _logger.LogWarning("Folder does not exist: {FolderPath}", folderPath);
                    return 0;
                }

                var files = Directory.GetFiles(folderPath, "*.*", SearchOption.AllDirectories)
                    .Where(f => f.EndsWith(".txt", StringComparison.OrdinalIgnoreCase) || 
                               f.EndsWith(".pdf", StringComparison.OrdinalIgnoreCase))
                    .ToList();

                var processedCount = 0;

                foreach (var file in files)
                {
                    try
                    {
                        var content = await ExtractTextFromFileAsync(file);
                        if (!string.IsNullOrWhiteSpace(content))
                        {
                            await IndexDocumentAsync(file, content);
                            processedCount++;
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error processing file: {FilePath}", file);
                    }
                }

                _logger.LogInformation("Indexed {ProcessedCount} documents from folder", processedCount);
                return processedCount;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error indexing documents from folder");
                return 0;
            }
        }

        public async Task<int> IndexDocumentsFromSharePointAsync(string siteUrl, string libraryName)
        {
            try
            {
                _logger.LogInformation("Indexing documents from SharePoint: {SiteUrl}/{LibraryName}", siteUrl, libraryName);

                var documents = await _sharePointService.GetDocumentsAsync(siteUrl, libraryName);
                var processedCount = 0;

                foreach (var document in documents)
                {
                    try
                    {
                        var content = await _sharePointService.GetDocumentContentAsync(document.Url);
                        if (!string.IsNullOrWhiteSpace(content))
                        {
                            await IndexDocumentAsync(document.Name, content);
                            processedCount++;
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error processing SharePoint document: {DocumentName}", document.Name);
                    }
                }

                _logger.LogInformation("Indexed {ProcessedCount} documents from SharePoint", processedCount);
                return processedCount;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error indexing documents from SharePoint");
                return 0;
            }
        }

        public async Task<object> GetIndexStatusAsync()
        {
            var chunkCount = await _vectorStoreService.GetChunkCountAsync();

            return new
            {
                TotalChunks = chunkCount,
                LastUpdated = DateTime.UtcNow,
                Status = chunkCount > 0 ? "Ready" : "Empty"
            };
        }

        private async Task<string> ExtractTextFromFileAsync(string filePath)
        {
            var extension = Path.GetExtension(filePath).ToLower();

            switch (extension)
            {
                case ".txt":
                    return await File.ReadAllTextAsync(filePath);

                case ".pdf":
                    return ExtractTextFromPdf(filePath);

                default:
                    _logger.LogWarning("Unsupported file type: {Extension}", extension);
                    return string.Empty;
            }
        }

        private string ExtractTextFromPdf(string filePath)
        {
            try
            {
                using var reader = new PdfReader(filePath);
                var text = new StringBuilder();

                for (int page = 1; page <= reader.NumberOfPages; page++)
                {
                    text.Append(PdfTextExtractor.GetTextFromPage(reader, page));
                    text.Append(" ");
                }

                return text.ToString();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error extracting text from PDF: {FilePath}", filePath);
                return string.Empty;
            }
        }

        private async Task IndexDocumentAsync(string documentName, string content)
        {
            // Split content into chunks
            var chunks = SplitIntoChunks(content, maxChunkSize: 1000, overlap: 200);

            var documentChunks = new List<DocumentChunk>();

            for (int i = 0; i < chunks.Count; i++)
            {
                var embedding = await _embeddingService.GenerateEmbeddingAsync(chunks[i]);

                var chunk = new DocumentChunk
                {
                    Id = $"{documentName}_{i}",
                    Content = chunks[i],
                    SourceDocument = documentName,
                    Embedding = embedding,
                    Metadata = new Dictionary<string, object>
                    {
                        ["ChunkIndex"] = i,
                        ["TotalChunks"] = chunks.Count,
                        ["IndexedAt"] = DateTime.UtcNow
                    }
                };

                documentChunks.Add(chunk);
            }

            await _vectorStoreService.AddChunksAsync(documentChunks);
            _logger.LogDebug("Indexed {ChunkCount} chunks for document: {DocumentName}", documentChunks.Count, documentName);
        }

        private List<string> SplitIntoChunks(string text, int maxChunkSize = 1000, int overlap = 200)
        {
            var chunks = new List<string>();
            var sentences = text.Split(new[] { '.', '!', '?' }, StringSplitOptions.RemoveEmptyEntries);

            var currentChunk = new StringBuilder();

            foreach (var sentence in sentences)
            {
                var trimmedSentence = sentence.Trim();
                if (string.IsNullOrEmpty(trimmedSentence)) continue;

                if (currentChunk.Length + trimmedSentence.Length + 1 > maxChunkSize && currentChunk.Length > 0)
                {
                    chunks.Add(currentChunk.ToString().Trim());

                    // Handle overlap
                    var words = currentChunk.ToString().Split(' ');
                    var overlapWords = words.TakeLast(Math.Min(overlap / 5, words.Length / 2)).ToArray();
                    currentChunk.Clear();
                    if (overlapWords.Any())
                    {
                        currentChunk.Append(string.Join(" ", overlapWords) + " ");
                    }
                }

                currentChunk.Append(trimmedSentence + ". ");
            }

            if (currentChunk.Length > 0)
            {
                chunks.Add(currentChunk.ToString().Trim());
            }

            return chunks;
        }

        private string BuildPrompt(string question, string context)
        {
            return $@"Context information is below:
{context}

Given the context information and not prior knowledge, answer the question: {question}

If the context doesn't contain relevant information to answer the question, please say so clearly.";
        }
    }
}
