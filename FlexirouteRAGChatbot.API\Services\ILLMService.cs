namespace FlexirouteRAGChatbot.API.Services
{
    public class LLMRequest
    {
        public string Prompt { get; set; } = string.Empty;
        public string SystemMessage { get; set; } = string.Empty;
        public float Temperature { get; set; } = 0.7f;
        public int MaxTokens { get; set; } = 1000;
    }

    public class LLMResponse
    {
        public string Content { get; set; } = string.Empty;
        public bool IsSuccess { get; set; }
        public string ErrorMessage { get; set; } = string.Empty;
        public int TokensUsed { get; set; }
    }

    public interface ILLMService
    {
        /// <summary>
        /// Generates a response from the LLM based on the provided request
        /// </summary>
        /// <param name="request">LLM request containing prompt and parameters</param>
        /// <returns>LLM response</returns>
        Task<LLMResponse> GenerateResponseAsync(LLMRequest request);

        /// <summary>
        /// Checks if the LLM service is available
        /// </summary>
        /// <returns>True if service is available, false otherwise</returns>
        Task<bool> IsAvailableAsync();
    }
}
