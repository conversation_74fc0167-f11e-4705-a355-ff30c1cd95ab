using FlexirouteRAGChatbot.API.Services;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddControllers();
builder.Services.AddOpenApi();

// Add CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowBlazorApp", policy =>
    {
        policy.WithOrigins("https://localhost:7001", "http://localhost:5001")
              .AllowAnyHeader()
              .AllowAnyMethod();
    });
});

// Register RAG services
builder.Services.AddScoped<IEmbeddingService, MockEmbeddingService>();
builder.Services.AddSingleton<IVectorStoreService, InMemoryVectorStoreService>();
builder.Services.AddScoped<ILLMService, MockLLMService>();
builder.Services.AddScoped<ISharePointService, MockSharePointService>();
builder.Services.AddScoped<IRagService, RagService>();

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.MapOpenApi();
}

app.UseHttpsRedirection();
app.UseCors("AllowBlazorApp");

app.UseRouting();
app.MapControllers();

app.Run();
