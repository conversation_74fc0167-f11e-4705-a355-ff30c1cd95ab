{"format": 1, "restore": {"C:\\Development\\Flexiroute Knowladge Base AI Chat Bot\\FlexirouteRAGChatbot.Web\\FlexirouteRAGChatbot.Web.csproj": {}}, "projects": {"C:\\Development\\Flexiroute Knowladge Base AI Chat Bot\\FlexirouteRAGChatbot.Web\\FlexirouteRAGChatbot.Web.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Development\\Flexiroute Knowladge Base AI Chat Bot\\FlexirouteRAGChatbot.Web\\FlexirouteRAGChatbot.Web.csproj", "projectName": "FlexirouteRAGChatbot.Web", "projectPath": "C:\\Development\\Flexiroute Knowladge Base AI Chat Bot\\FlexirouteRAGChatbot.Web\\FlexirouteRAGChatbot.Web.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Development\\Flexiroute Knowladge Base AI Chat Bot\\FlexirouteRAGChatbot.Web\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\DevExpress 21.2\\Components\\Offline Packages", "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 19.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 21.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\DevExpress 19.2\\Components\\System\\Components\\Packages": {}, "C:\\Program Files (x86)\\DevExpress 21.2\\Components\\System\\Components\\Packages": {}, "C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.Extensions.Http": {"target": "Package", "version": "[9.0.7, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}}}