namespace FlexirouteRAGChatbot.Web.Models
{
    public class ChatMessage
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string Content { get; set; } = string.Empty;
        public bool IsFromUser { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.Now;
        public List<string>? SourceDocuments { get; set; }
        public bool IsTyping { get; set; }
        public string? ConversationId { get; set; }
    }
}
