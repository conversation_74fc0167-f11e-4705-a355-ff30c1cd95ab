is_global = true
build_property.TargetFramework = net9.0
build_property.TargetPlatformMinVersion = 
build_property.UsingMicrosoftNETSdkWeb = true
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = 
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.RootNamespace = FlexirouteRAGChatbot.Web
build_property.RootNamespace = FlexirouteRAGChatbot.Web
build_property.ProjectDir = C:\Development\Flexiroute Knowladge Base AI Chat Bot\FlexirouteRAGChatbot.Web\
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = 
build_property.RazorLangVersion = 9.0
build_property.SupportLocalizedComponentNames = 
build_property.GenerateRazorMetadataSourceChecksumAttributes = 
build_property.MSBuildProjectDirectory = C:\Development\Flexiroute Knowladge Base AI Chat Bot\FlexirouteRAGChatbot.Web
build_property._RazorSourceGeneratorDebug = 
build_property.EffectiveAnalysisLevelStyle = 9.0
build_property.EnableCodeStyleSeverity = 

[C:/Development/Flexiroute Knowladge Base AI Chat Bot/FlexirouteRAGChatbot.Web/App.razor]
build_metadata.AdditionalFiles.TargetPath = QXBwLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Development/Flexiroute Knowladge Base AI Chat Bot/FlexirouteRAGChatbot.Web/Components/Chatbot.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xDaGF0Ym90LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Development/Flexiroute Knowladge Base AI Chat Bot/FlexirouteRAGChatbot.Web/Components/MessageBubble.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xNZXNzYWdlQnViYmxlLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Development/Flexiroute Knowladge Base AI Chat Bot/FlexirouteRAGChatbot.Web/Pages/Counter.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQ291bnRlci5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Development/Flexiroute Knowladge Base AI Chat Bot/FlexirouteRAGChatbot.Web/Pages/FetchData.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcRmV0Y2hEYXRhLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Development/Flexiroute Knowladge Base AI Chat Bot/FlexirouteRAGChatbot.Web/Pages/Index.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcSW5kZXgucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Development/Flexiroute Knowladge Base AI Chat Bot/FlexirouteRAGChatbot.Web/Shared/SurveyPrompt.razor]
build_metadata.AdditionalFiles.TargetPath = U2hhcmVkXFN1cnZleVByb21wdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Development/Flexiroute Knowladge Base AI Chat Bot/FlexirouteRAGChatbot.Web/_Imports.razor]
build_metadata.AdditionalFiles.TargetPath = X0ltcG9ydHMucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Development/Flexiroute Knowladge Base AI Chat Bot/FlexirouteRAGChatbot.Web/Shared/MainLayout.razor]
build_metadata.AdditionalFiles.TargetPath = U2hhcmVkXE1haW5MYXlvdXQucmF6b3I=
build_metadata.AdditionalFiles.CssScope = b-wds4ewpxba

[C:/Development/Flexiroute Knowladge Base AI Chat Bot/FlexirouteRAGChatbot.Web/Shared/NavMenu.razor]
build_metadata.AdditionalFiles.TargetPath = U2hhcmVkXE5hdk1lbnUucmF6b3I=
build_metadata.AdditionalFiles.CssScope = b-wj6rg47v2t

[C:/Development/Flexiroute Knowladge Base AI Chat Bot/FlexirouteRAGChatbot.Web/Pages/Error.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcRXJyb3IuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[C:/Development/Flexiroute Knowladge Base AI Chat Bot/FlexirouteRAGChatbot.Web/Pages/_Host.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcX0hvc3QuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 
