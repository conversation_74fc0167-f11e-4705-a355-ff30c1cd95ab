using iTextSharp.text.pdf;
using iTextSharp.text.pdf.parser;

namespace FlexirouteRAGChatbot.API.Services
{
    public class ChatRequest
    {
        public string Question { get; set; } = string.Empty;
        public string? ConversationId { get; set; }
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    public class ChatResponse
    {
        public string Answer { get; set; } = string.Empty;
        public string ConversationId { get; set; } = string.Empty;
        public IEnumerable<string> SourceDocuments { get; set; } = new List<string>();
        public bool IsSuccess { get; set; }
        public string ErrorMessage { get; set; } = string.Empty;
        public int RetrievedChunks { get; set; }
    }

    public interface IRagService
    {
        /// <summary>
        /// Processes a chat request using the RAG pipeline
        /// </summary>
        /// <param name="request">Chat request containing the user's question</param>
        /// <returns>Chat response with answer and source information</returns>
        Task<ChatResponse> ProcessChatRequestAsync(ChatRequest request);

        /// <summary>
        /// Indexes documents from a local folder
        /// </summary>
        /// <param name="folderPath">Path to the folder containing documents</param>
        /// <returns>Number of documents processed</returns>
        Task<int> IndexDocumentsFromFolderAsync(string folderPath);

        /// <summary>
        /// Indexes documents from SharePoint
        /// </summary>
        /// <param name="siteUrl">SharePoint site URL</param>
        /// <param name="libraryName">Document library name</param>
        /// <returns>Number of documents processed</returns>
        Task<int> IndexDocumentsFromSharePointAsync(string siteUrl, string libraryName);

        /// <summary>
        /// Gets the current status of the document index
        /// </summary>
        /// <returns>Index status information</returns>
        Task<object> GetIndexStatusAsync();
    }
}
