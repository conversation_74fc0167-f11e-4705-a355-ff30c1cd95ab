using System.Security.Cryptography;
using System.Text;

namespace FlexirouteRAGChatbot.API.Services
{
    public class MockEmbeddingService : IEmbeddingService
    {
        private const int EmbeddingDimension = 384; // Common embedding dimension
        private readonly Random _random;

        public MockEmbeddingService()
        {
            _random = new Random(42); // Fixed seed for consistent results
        }

        public Task<float[]> GenerateEmbeddingAsync(string text)
        {
            // Generate a deterministic "embedding" based on text content
            // In a real implementation, this would call an actual embedding model
            var embedding = GenerateDeterministicEmbedding(text);
            return Task.FromResult(embedding);
        }

        public async Task<IEnumerable<float[]>> GenerateEmbeddingsAsync(IEnumerable<string> texts)
        {
            var embeddings = new List<float[]>();
            foreach (var text in texts)
            {
                var embedding = await GenerateEmbeddingAsync(text);
                embeddings.Add(embedding);
            }
            return embeddings;
        }

        private float[] GenerateDeterministicEmbedding(string text)
        {
            // Create a hash of the text to ensure deterministic results
            using var sha256 = SHA256.Create();
            var hash = sha256.ComputeHash(Encoding.UTF8.GetBytes(text));
            
            // Use the hash to seed a random number generator
            var seed = BitConverter.ToInt32(hash, 0);
            var random = new Random(seed);
            
            var embedding = new float[EmbeddingDimension];
            
            // Generate random values and normalize
            var sum = 0.0;
            for (int i = 0; i < EmbeddingDimension; i++)
            {
                embedding[i] = (float)(random.NextDouble() * 2.0 - 1.0); // Range [-1, 1]
                sum += embedding[i] * embedding[i];
            }
            
            // Normalize the vector
            var magnitude = Math.Sqrt(sum);
            if (magnitude > 0)
            {
                for (int i = 0; i < EmbeddingDimension; i++)
                {
                    embedding[i] = (float)(embedding[i] / magnitude);
                }
            }
            
            return embedding;
        }
    }
}
