namespace FlexirouteRAGChatbot.API.Services
{
    public class MockSharePointService : ISharePointService
    {
        private readonly ILogger<MockSharePointService> _logger;

        public MockSharePointService(ILogger<MockSharePointService> logger)
        {
            _logger = logger;
        }

        public Task<IEnumerable<SharePointDocument>> GetDocumentsAsync(string siteUrl, string libraryName)
        {
            _logger.LogInformation("Mock SharePoint service - GetDocumentsAsync called for {SiteUrl}/{LibraryName}", siteUrl, libraryName);

            // Return mock documents for demonstration
            var mockDocuments = new List<SharePointDocument>
            {
                new SharePointDocument
                {
                    Id = "1",
                    Name = "Company Policy.docx",
                    Content = "This is a mock SharePoint document containing company policies and procedures.",
                    Url = $"{siteUrl}/{libraryName}/Company Policy.docx",
                    LastModified = DateTime.Now.AddDays(-30),
                    ContentType = "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                    Size = 1024
                },
                new SharePointDocument
                {
                    Id = "2",
                    Name = "Training Manual.pdf",
                    Content = "This is a mock SharePoint document containing training materials and guidelines.",
                    Url = $"{siteUrl}/{libraryName}/Training Manual.pdf",
                    LastModified = DateTime.Now.AddDays(-15),
                    ContentType = "application/pdf",
                    Size = 2048
                }
            };

            return Task.FromResult<IEnumerable<SharePointDocument>>(mockDocuments);
        }

        public Task<string> GetDocumentContentAsync(string documentUrl)
        {
            _logger.LogInformation("Mock SharePoint service - GetDocumentContentAsync called for {DocumentUrl}", documentUrl);

            // Return mock content based on URL
            if (documentUrl.Contains("Company Policy"))
            {
                return Task.FromResult("Company Policy Document\n\nThis document outlines the policies and procedures for all employees. Please ensure compliance with all guidelines.");
            }
            else if (documentUrl.Contains("Training Manual"))
            {
                return Task.FromResult("Training Manual\n\nThis manual provides comprehensive training materials for new employees and ongoing professional development.");
            }

            return Task.FromResult("Mock document content from SharePoint.");
        }

        public Task<bool> IsAvailableAsync()
        {
            // Mock service is always available for demonstration
            _logger.LogInformation("Mock SharePoint service - IsAvailableAsync called");
            return Task.FromResult(true);
        }
    }
}
