namespace FlexirouteRAGChatbot.API.Services
{
    public class MockLLMService : ILLMService
    {
        private readonly ILogger<MockLLMService> _logger;
        private readonly Random _random;

        public MockLLMService(ILogger<MockLLMService> logger)
        {
            _logger = logger;
            _random = new Random();
        }

        public Task<LLMResponse> GenerateResponseAsync(LLMRequest request)
        {
            _logger.LogDebug("Generating mock LLM response for prompt: {Prompt}", request.Prompt);

            // Simulate processing delay
            var delay = _random.Next(500, 2000);
            Thread.Sleep(delay);

            // Generate a mock response based on the prompt content
            var response = GenerateMockResponse(request.Prompt);

            return Task.FromResult(new LLMResponse
            {
                Content = response,
                IsSuccess = true,
                TokensUsed = response.Split(' ').Length + request.Prompt.Split(' ').Length
            });
        }

        public Task<bool> IsAvailableAsync()
        {
            // Mock service is always available
            return Task.FromResult(true);
        }

        private string GenerateMockResponse(string prompt)
        {
            // Simple keyword-based response generation for demo purposes
            var lowerPrompt = prompt.ToLower();

            if (lowerPrompt.Contains("flexiroute"))
            {
                return "Flexiroute is a comprehensive logistics and transportation management platform that helps businesses optimize their delivery routes, manage fleets, and coordinate supply chains. Our platform offers real-time tracking, analytics, and customer portal features.";
            }

            if (lowerPrompt.Contains("route") || lowerPrompt.Contains("optimization"))
            {
                return "Route optimization is one of Flexiroute's core features. Our advanced algorithms analyze traffic patterns, delivery windows, vehicle capacity, and other factors to find the most efficient delivery routes, helping reduce fuel costs and improve delivery times.";
            }

            if (lowerPrompt.Contains("tracking") || lowerPrompt.Contains("monitor"))
            {
                return "Flexiroute provides real-time tracking capabilities that allow you to monitor vehicles and shipments throughout the delivery process. This includes GPS tracking, delivery status updates, and notifications for customers.";
            }

            if (lowerPrompt.Contains("contact") || lowerPrompt.Contains("support"))
            {
                return "For support, you can contact <NAME_EMAIL> or call +1-800-FLEXIROUTE. For technical issues, reach <NAME_EMAIL> or use our 24/7 helpline.";
            }

            if (lowerPrompt.Contains("fleet") || lowerPrompt.Contains("vehicle"))
            {
                return "Flexiroute's fleet management tools provide comprehensive vehicle tracking, maintenance scheduling, driver management, and performance analytics to help you optimize your fleet operations and reduce operational costs.";
            }

            // Default response
            return "Based on the available information in our knowledge base, I can help you with questions about Flexiroute's logistics and transportation management services. Please feel free to ask about specific features, services, or contact information.";
        }
    }
}
