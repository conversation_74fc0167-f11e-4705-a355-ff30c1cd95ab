@using FlexirouteRAGChatbot.Web.Models

<div class="message-bubble @(Message.IsFromUser ? "user-message" : "bot-message")">
    <div class="message-header">
        <span class="message-sender">@(Message.IsFromUser ? "You" : "Flexiroute Assistant")</span>
        <span class="message-time">@Message.Timestamp.ToString("HH:mm")</span>
    </div>
    <div class="message-content">
        @Message.Content
    </div>
    @if (!Message.IsFromUser && Message.SourceDocuments?.Any() == true)
    {
        <div class="message-sources">
            <small class="text-muted">
                Sources: @string.Join(", ", Message.SourceDocuments)
            </small>
        </div>
    }
</div>

<style>
    .message-bubble {
        margin: 10px 0;
        padding: 12px 16px;
        border-radius: 18px;
        max-width: 80%;
        word-wrap: break-word;
    }

    .user-message {
        background-color: #007bff;
        color: white;
        margin-left: auto;
        margin-right: 0;
        text-align: right;
    }

    .bot-message {
        background-color: #f8f9fa;
        color: #333;
        border: 1px solid #e9ecef;
        margin-left: 0;
        margin-right: auto;
    }

    .message-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 6px;
        font-size: 0.85em;
    }

    .message-sender {
        font-weight: 600;
    }

    .user-message .message-sender,
    .user-message .message-time {
        color: rgba(255, 255, 255, 0.8);
    }

    .bot-message .message-sender,
    .bot-message .message-time {
        color: #6c757d;
    }

    .message-content {
        line-height: 1.4;
        white-space: pre-wrap;
    }

    .message-sources {
        margin-top: 8px;
        padding-top: 8px;
        border-top: 1px solid #e9ecef;
        font-size: 0.8em;
    }

    .text-muted {
        color: #6c757d !important;
    }

    @media (max-width: 768px) {
        .message-bubble {
            max-width: 95%;
        }
    }
</style>

@code {
    [Parameter] public ChatMessage Message { get; set; } = new();
}
