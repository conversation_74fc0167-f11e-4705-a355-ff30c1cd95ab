using FlexirouteRAGChatbot.API.Services;
using Microsoft.AspNetCore.Mvc;

namespace FlexirouteRAGChatbot.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class ChatController : ControllerBase
    {
        private readonly IRagService _ragService;
        private readonly ILogger<ChatController> _logger;

        public ChatController(IRagService ragService, ILogger<ChatController> logger)
        {
            _ragService = ragService;
            _logger = logger;
        }

        [HttpPost]
        public async Task<IActionResult> Chat([FromBody] ChatRequestDto request)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(request.Question))
                {
                    return BadRequest(new
                    {
                        Success = false,
                        Message = "Question is required"
                    });
                }

                _logger.LogInformation("Processing chat request: {Question}", request.Question);

                var chatRequest = new ChatRequest
                {
                    Question = request.Question,
                    ConversationId = request.ConversationId,
                    Metadata = request.Metadata ?? new Dictionary<string, object>()
                };

                var response = await _ragService.ProcessChatRequestAsync(chatRequest);

                return Ok(new ChatResponseDto
                {
                    Answer = response.Answer,
                    ConversationId = response.ConversationId,
                    SourceDocuments = response.SourceDocuments.ToList(),
                    Success = response.IsSuccess,
                    ErrorMessage = response.ErrorMessage,
                    RetrievedChunks = response.RetrievedChunks,
                    Timestamp = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing chat request");
                return StatusCode(500, new ChatResponseDto
                {
                    Answer = "I'm sorry, but I encountered an error while processing your request.",
                    Success = false,
                    ErrorMessage = ex.Message,
                    Timestamp = DateTime.UtcNow
                });
            }
        }

        [HttpGet("health")]
        public async Task<IActionResult> Health()
        {
            try
            {
                var indexStatus = await _ragService.GetIndexStatusAsync();
                return Ok(new
                {
                    Status = "Healthy",
                    Timestamp = DateTime.UtcNow,
                    IndexStatus = indexStatus
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Health check failed");
                return StatusCode(500, new
                {
                    Status = "Unhealthy",
                    Message = ex.Message,
                    Timestamp = DateTime.UtcNow
                });
            }
        }
    }

    public class ChatRequestDto
    {
        public string Question { get; set; } = string.Empty;
        public string? ConversationId { get; set; }
        public Dictionary<string, object>? Metadata { get; set; }
    }

    public class ChatResponseDto
    {
        public string Answer { get; set; } = string.Empty;
        public string ConversationId { get; set; } = string.Empty;
        public List<string> SourceDocuments { get; set; } = new();
        public bool Success { get; set; }
        public string ErrorMessage { get; set; } = string.Empty;
        public int RetrievedChunks { get; set; }
        public DateTime Timestamp { get; set; }
    }
}
