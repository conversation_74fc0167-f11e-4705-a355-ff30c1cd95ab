using System.Text;
using System.Text.Json;

namespace FlexirouteRAGChatbot.Web.Services
{
    public class ChatApiService
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<ChatApiService> _logger;
        private readonly JsonSerializerOptions _jsonOptions;

        public ChatApiService(HttpClient httpClient, ILogger<ChatApiService> logger)
        {
            _httpClient = httpClient;
            _logger = logger;
            _jsonOptions = new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                PropertyNameCaseInsensitive = true
            };
        }

        public async Task<ChatResponse> SendMessageAsync(string message, string? conversationId = null)
        {
            try
            {
                var request = new ChatRequest
                {
                    Question = message,
                    ConversationId = conversationId
                };

                var json = JsonSerializer.Serialize(request, _jsonOptions);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                _logger.LogInformation("Sending chat request: {Message}", message);

                var response = await _httpClient.PostAsync("api/chat", content);
                
                if (response.IsSuccessStatusCode)
                {
                    var responseJson = await response.Content.ReadAsStringAsync();
                    var chatResponse = JsonSerializer.Deserialize<ChatResponse>(responseJson, _jsonOptions);
                    return chatResponse ?? new ChatResponse { Success = false, ErrorMessage = "Failed to deserialize response" };
                }
                else
                {
                    _logger.LogError("API request failed with status: {StatusCode}", response.StatusCode);
                    return new ChatResponse
                    {
                        Success = false,
                        ErrorMessage = $"API request failed with status: {response.StatusCode}"
                    };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending chat message");
                return new ChatResponse
                {
                    Success = false,
                    ErrorMessage = "Failed to connect to the chat service. Please try again."
                };
            }
        }

        public async Task<IndexResponse> IndexDocumentsAsync(string folderPath)
        {
            try
            {
                var request = new { FolderPath = folderPath };
                var json = JsonSerializer.Serialize(request, _jsonOptions);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                _logger.LogInformation("Indexing documents from folder: {FolderPath}", folderPath);

                var response = await _httpClient.PostAsync("api/documents/index-folder", content);
                
                if (response.IsSuccessStatusCode)
                {
                    var responseJson = await response.Content.ReadAsStringAsync();
                    var indexResponse = JsonSerializer.Deserialize<IndexResponse>(responseJson, _jsonOptions);
                    return indexResponse ?? new IndexResponse { Success = false, Message = "Failed to deserialize response" };
                }
                else
                {
                    return new IndexResponse
                    {
                        Success = false,
                        Message = $"API request failed with status: {response.StatusCode}"
                    };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error indexing documents");
                return new IndexResponse
                {
                    Success = false,
                    Message = "Failed to index documents. Please try again."
                };
            }
        }

        public async Task<object?> GetIndexStatusAsync()
        {
            try
            {
                var response = await _httpClient.GetAsync("api/documents/status");
                
                if (response.IsSuccessStatusCode)
                {
                    var responseJson = await response.Content.ReadAsStringAsync();
                    return JsonSerializer.Deserialize<object>(responseJson, _jsonOptions);
                }
                
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting index status");
                return null;
            }
        }
    }

    public class ChatRequest
    {
        public string Question { get; set; } = string.Empty;
        public string? ConversationId { get; set; }
        public Dictionary<string, object>? Metadata { get; set; }
    }

    public class ChatResponse
    {
        public string Answer { get; set; } = string.Empty;
        public string ConversationId { get; set; } = string.Empty;
        public List<string> SourceDocuments { get; set; } = new();
        public bool Success { get; set; }
        public string ErrorMessage { get; set; } = string.Empty;
        public int RetrievedChunks { get; set; }
        public DateTime Timestamp { get; set; }
    }

    public class IndexResponse
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public int ProcessedDocuments { get; set; }
    }
}
