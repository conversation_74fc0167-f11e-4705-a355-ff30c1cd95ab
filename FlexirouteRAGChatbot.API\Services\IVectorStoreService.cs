namespace FlexirouteRAGChatbot.API.Services
{
    public class DocumentChunk
    {
        public string Id { get; set; } = string.Empty;
        public string Content { get; set; } = string.Empty;
        public string SourceDocument { get; set; } = string.Empty;
        public float[] Embedding { get; set; } = Array.Empty<float>();
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    public class SearchResult
    {
        public DocumentChunk Chunk { get; set; } = new();
        public float Similarity { get; set; }
    }

    public interface IVectorStoreService
    {
        /// <summary>
        /// Adds a document chunk to the vector store
        /// </summary>
        /// <param name="chunk">Document chunk to add</param>
        Task AddChunkAsync(DocumentChunk chunk);

        /// <summary>
        /// Adds multiple document chunks to the vector store
        /// </summary>
        /// <param name="chunks">Collection of document chunks to add</param>
        Task AddChunksAsync(IEnumerable<DocumentChunk> chunks);

        /// <summary>
        /// Searches for similar chunks based on query embedding
        /// </summary>
        /// <param name="queryEmbedding">Query embedding vector</param>
        /// <param name="topK">Number of top results to return</param>
        /// <returns>Collection of search results ordered by similarity</returns>
        Task<IEnumerable<SearchResult>> SearchAsync(float[] queryEmbedding, int topK = 3);

        /// <summary>
        /// Clears all chunks from the vector store
        /// </summary>
        Task ClearAsync();

        /// <summary>
        /// Gets the total number of chunks in the store
        /// </summary>
        Task<int> GetChunkCountAsync();
    }
}
