using System.Collections.Concurrent;

namespace FlexirouteRAGChatbot.API.Services
{
    public class InMemoryVectorStoreService : IVectorStoreService
    {
        private readonly ConcurrentDictionary<string, DocumentChunk> _chunks;
        private readonly ILogger<InMemoryVectorStoreService> _logger;

        public InMemoryVectorStoreService(ILogger<InMemoryVectorStoreService> logger)
        {
            _chunks = new ConcurrentDictionary<string, DocumentChunk>();
            _logger = logger;
        }

        public Task AddChunkAsync(DocumentChunk chunk)
        {
            if (string.IsNullOrEmpty(chunk.Id))
            {
                chunk.Id = Guid.NewGuid().ToString();
            }

            _chunks.AddOrUpdate(chunk.Id, chunk, (key, oldValue) => chunk);
            _logger.LogDebug("Added chunk {ChunkId} from document {SourceDocument}", chunk.Id, chunk.SourceDocument);
            
            return Task.CompletedTask;
        }

        public async Task AddChunksAsync(IEnumerable<DocumentChunk> chunks)
        {
            foreach (var chunk in chunks)
            {
                await AddChunkAsync(chunk);
            }
        }

        public Task<IEnumerable<SearchResult>> SearchAsync(float[] queryEmbedding, int topK = 3)
        {
            if (queryEmbedding == null || queryEmbedding.Length == 0)
            {
                return Task.FromResult(Enumerable.Empty<SearchResult>());
            }

            var results = _chunks.Values
                .Select(chunk => new SearchResult
                {
                    Chunk = chunk,
                    Similarity = CalculateCosineSimilarity(queryEmbedding, chunk.Embedding)
                })
                .OrderByDescending(r => r.Similarity)
                .Take(topK);

            _logger.LogDebug("Found {ResultCount} results for query", results.Count());
            
            return Task.FromResult(results);
        }

        public Task ClearAsync()
        {
            _chunks.Clear();
            _logger.LogInformation("Cleared all chunks from vector store");
            return Task.CompletedTask;
        }

        public Task<int> GetChunkCountAsync()
        {
            return Task.FromResult(_chunks.Count);
        }

        private static float CalculateCosineSimilarity(float[] vectorA, float[] vectorB)
        {
            if (vectorA.Length != vectorB.Length)
            {
                return 0f;
            }

            var dotProduct = 0.0;
            var magnitudeA = 0.0;
            var magnitudeB = 0.0;

            for (int i = 0; i < vectorA.Length; i++)
            {
                dotProduct += vectorA[i] * vectorB[i];
                magnitudeA += vectorA[i] * vectorA[i];
                magnitudeB += vectorB[i] * vectorB[i];
            }

            if (magnitudeA == 0.0 || magnitudeB == 0.0)
            {
                return 0f;
            }

            return (float)(dotProduct / (Math.Sqrt(magnitudeA) * Math.Sqrt(magnitudeB)));
        }
    }
}
