@using FlexirouteRAGChatbot.Web.Models
@using FlexirouteRAGChatbot.Web.Services
@using FlexirouteRAGChatbot.Web.Components
@inject ChatApiService ChatApi
@inject IJSRuntime JSRuntime

<div class="chatbot-container">
    <div class="chat-header">
        <h4><i class="fas fa-robot"></i> Flexiroute Knowledge Assistant</h4>
        <div class="chat-actions">
            <button class="btn btn-sm btn-outline-secondary" @onclick="IndexDocuments" disabled="@isIndexing">
                @if (isIndexing)
                {
                    <span class="spinner-border spinner-border-sm" role="status"></span>
                    <span>Indexing...</span>
                }
                else
                {
                    <i class="fas fa-sync"></i>
                    <span>Index Documents</span>
                }
            </button>
        </div>
    </div>

    <div class="chat-messages" @ref="messagesContainer">
        @foreach (var message in messages)
        {
            <MessageBubble Message="message" />
        }
        
        @if (isTyping)
        {
            <div class="typing-indicator">
                <div class="typing-bubble">
                    <div class="typing-dots">
                        <span></span>
                        <span></span>
                        <span></span>
                    </div>
                    <small class="text-muted">Flexiroute Assistant is typing...</small>
                </div>
            </div>
        }
    </div>

    <div class="chat-input">
        <div class="input-group">
            <input type="text" class="form-control" placeholder="Ask me anything about Flexiroute..." 
                   @bind="currentMessage" @onkeypress="HandleKeyPress" disabled="@isTyping" />
            <button class="btn btn-primary" @onclick="SendMessage" disabled="@(isTyping || string.IsNullOrWhiteSpace(currentMessage))">
                @if (isTyping)
                {
                    <span class="spinner-border spinner-border-sm" role="status"></span>
                }
                else
                {
                    <i class="fas fa-paper-plane"></i>
                }
            </button>
        </div>
    </div>
</div>

@code {
    private List<ChatMessage> messages = new();
    private string currentMessage = string.Empty;
    private bool isTyping = false;
    private bool isIndexing = false;
    private string? conversationId;
    private ElementReference messagesContainer;

    protected override async Task OnInitializedAsync()
    {
        // Add welcome message
        messages.Add(new ChatMessage
        {
            Content = "Hello! I'm your Flexiroute Knowledge Assistant. I can help you with questions about our logistics and transportation services. How can I assist you today?",
            IsFromUser = false,
            Timestamp = DateTime.Now
        });
    }

    private async Task SendMessage()
    {
        if (string.IsNullOrWhiteSpace(currentMessage) || isTyping)
            return;

        var userMessage = new ChatMessage
        {
            Content = currentMessage,
            IsFromUser = true,
            ConversationId = conversationId
        };

        messages.Add(userMessage);
        var messageToSend = currentMessage;
        currentMessage = string.Empty;
        isTyping = true;

        StateHasChanged();
        await ScrollToBottom();

        try
        {
            var response = await ChatApi.SendMessageAsync(messageToSend, conversationId);
            
            if (response.Success)
            {
                conversationId = response.ConversationId;
                
                var botMessage = new ChatMessage
                {
                    Content = response.Answer,
                    IsFromUser = false,
                    ConversationId = response.ConversationId,
                    SourceDocuments = response.SourceDocuments
                };

                messages.Add(botMessage);
            }
            else
            {
                var errorMessage = new ChatMessage
                {
                    Content = $"Sorry, I encountered an error: {response.ErrorMessage}",
                    IsFromUser = false
                };

                messages.Add(errorMessage);
            }
        }
        catch (Exception ex)
        {
            var errorMessage = new ChatMessage
            {
                Content = "Sorry, I'm having trouble connecting to the service. Please try again.",
                IsFromUser = false
            };

            messages.Add(errorMessage);
        }
        finally
        {
            isTyping = false;
            StateHasChanged();
            await ScrollToBottom();
        }
    }

    private async Task HandleKeyPress(KeyboardEventArgs e)
    {
        if (e.Key == "Enter" && !e.ShiftKey)
        {
            await SendMessage();
        }
    }

    private async Task IndexDocuments()
    {
        isIndexing = true;
        StateHasChanged();

        try
        {
            // Use the Documents folder in the project root
            var documentsPath = Path.Combine(Directory.GetCurrentDirectory(), "..", "Documents");
            var response = await ChatApi.IndexDocumentsAsync(documentsPath);
            
            var statusMessage = new ChatMessage
            {
                Content = response.Success 
                    ? $"✅ Successfully indexed {response.ProcessedDocuments} documents!" 
                    : $"❌ Failed to index documents: {response.Message}",
                IsFromUser = false
            };

            messages.Add(statusMessage);
        }
        catch (Exception ex)
        {
            var errorMessage = new ChatMessage
            {
                Content = $"❌ Error indexing documents: {ex.Message}",
                IsFromUser = false
            };

            messages.Add(errorMessage);
        }
        finally
        {
            isIndexing = false;
            StateHasChanged();
            await ScrollToBottom();
        }
    }

    private async Task ScrollToBottom()
    {
        await Task.Delay(50); // Small delay to ensure DOM is updated
        await JSRuntime.InvokeVoidAsync("scrollToBottom", messagesContainer);
    }
}

<style>
    .chatbot-container {
        display: flex;
        flex-direction: column;
        height: 80vh;
        max-width: 800px;
        margin: 0 auto;
        border: 1px solid #e9ecef;
        border-radius: 10px;
        overflow: hidden;
        background: white;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .chat-header {
        background: linear-gradient(135deg, #007bff, #0056b3);
        color: white;
        padding: 15px 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .chat-header h4 {
        margin: 0;
        font-size: 1.1rem;
    }

    .chat-actions button {
        color: white;
        border-color: rgba(255, 255, 255, 0.3);
    }

    .chat-actions button:hover {
        background-color: rgba(255, 255, 255, 0.1);
        border-color: rgba(255, 255, 255, 0.5);
    }

    .chat-messages {
        flex: 1;
        overflow-y: auto;
        padding: 20px;
        background-color: #f8f9fa;
    }

    .chat-input {
        padding: 15px 20px;
        background: white;
        border-top: 1px solid #e9ecef;
    }

    .typing-indicator {
        display: flex;
        justify-content: flex-start;
        margin: 10px 0;
    }

    .typing-bubble {
        background-color: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 18px;
        padding: 12px 16px;
        max-width: 80%;
    }

    .typing-dots {
        display: flex;
        gap: 4px;
        margin-bottom: 4px;
    }

    .typing-dots span {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background-color: #6c757d;
        animation: typing 1.4s infinite ease-in-out;
    }

    .typing-dots span:nth-child(1) { animation-delay: -0.32s; }
    .typing-dots span:nth-child(2) { animation-delay: -0.16s; }

    @keyframes typing {
        0%, 80%, 100% {
            transform: scale(0.8);
            opacity: 0.5;
        }
        40% {
            transform: scale(1);
            opacity: 1;
        }
    }

    @media (max-width: 768px) {
        .chatbot-container {
            height: 90vh;
            margin: 10px;
            border-radius: 0;
        }

        .chat-header {
            padding: 10px 15px;
        }

        .chat-header h4 {
            font-size: 1rem;
        }

        .chat-messages {
            padding: 15px;
        }

        .chat-input {
            padding: 10px 15px;
        }
    }
</style>
