namespace FlexirouteRAGChatbot.API.Services
{
    public interface IEmbeddingService
    {
        /// <summary>
        /// Generates embeddings for the given text
        /// </summary>
        /// <param name="text">Text to embed</param>
        /// <returns>Float array representing the embedding vector</returns>
        Task<float[]> GenerateEmbeddingAsync(string text);

        /// <summary>
        /// Generates embeddings for multiple texts
        /// </summary>
        /// <param name="texts">Collection of texts to embed</param>
        /// <returns>Collection of embedding vectors</returns>
        Task<IEnumerable<float[]>> GenerateEmbeddingsAsync(IEnumerable<string> texts);
    }
}
